{"cells": [{"cell_type": "code", "execution_count": 3, "id": "3b500de4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import pickle\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import LabelEncoder,OneHotEncoder,StandardScaler\n"]}, {"cell_type": "code", "execution_count": null, "id": "5b5d1485", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Loan_ID</th>\n", "      <th>Gender</th>\n", "      <th>Married</th>\n", "      <th>Dependents</th>\n", "      <th>Education</th>\n", "      <th>Self_Employed</th>\n", "      <th>ApplicantIncome</th>\n", "      <th>CoapplicantIncome</th>\n", "      <th>LoanAmount</th>\n", "      <th><PERSON>an_Amount_Term</th>\n", "      <th>Credit_History</th>\n", "      <th>Property_Area</th>\n", "      <th>Loan_Status</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>LP001002</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>Graduate</td>\n", "      <td>No</td>\n", "      <td>5849</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>360.0</td>\n", "      <td>1.0</td>\n", "      <td>Urban</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>LP001003</td>\n", "      <td>Male</td>\n", "      <td>Yes</td>\n", "      <td>1</td>\n", "      <td>Graduate</td>\n", "      <td>No</td>\n", "      <td>4583</td>\n", "      <td>1508.0</td>\n", "      <td>128.0</td>\n", "      <td>360.0</td>\n", "      <td>1.0</td>\n", "      <td>Rural</td>\n", "      <td>N</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>LP001005</td>\n", "      <td>Male</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>Graduate</td>\n", "      <td>Yes</td>\n", "      <td>3000</td>\n", "      <td>0.0</td>\n", "      <td>66.0</td>\n", "      <td>360.0</td>\n", "      <td>1.0</td>\n", "      <td>Urban</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>LP001006</td>\n", "      <td>Male</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>Not Graduate</td>\n", "      <td>No</td>\n", "      <td>2583</td>\n", "      <td>2358.0</td>\n", "      <td>120.0</td>\n", "      <td>360.0</td>\n", "      <td>1.0</td>\n", "      <td>Urban</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>LP001008</td>\n", "      <td>Male</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>Graduate</td>\n", "      <td>No</td>\n", "      <td>6000</td>\n", "      <td>0.0</td>\n", "      <td>141.0</td>\n", "      <td>360.0</td>\n", "      <td>1.0</td>\n", "      <td>Urban</td>\n", "      <td>Y</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Loan_ID Gender Married  ... Credit_History Property_Area Loan_Status\n", "0  LP001002   Male      No  ...            1.0         Urban           Y\n", "1  LP001003   Male     Yes  ...            1.0         Rural           N\n", "2  LP001005   Male     Yes  ...            1.0         Urban           Y\n", "3  LP001006   Male     Yes  ...            1.0         Urban           Y\n", "4  LP001008   Male      No  ...            1.0         Urban           Y\n", "\n", "[5 rows x 13 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the dataset\n", "df = pd.read_csv('Lone.csv')\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5ed6f2bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Gender Married Dependents  ... Credit_History Property_Area  Loan_Status\n", "0   Male      No          0  ...            1.0         Urban            Y\n", "1   Male     Yes          1  ...            1.0         Rural            N\n", "2   Male     Yes          0  ...            1.0         Urban            Y\n", "3   Male     Yes          0  ...            1.0         Urban            Y\n", "4   Male      No          0  ...            1.0         Urban            Y\n", "\n", "[5 rows x 12 columns]\n"]}], "source": ["# Drop Irrelevant Column\n", "if 'Loan_ID' in df.columns:\n", "    df = df.drop('Loan_ID', axis=1)\n", "print(df.head())"]}, {"cell_type": "code", "execution_count": 9, "id": "36e19e0f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Gender Married Dependents  ... Credit_History Property_Area  Loan_Status\n", "0   Male      No          0  ...            1.0         Urban            Y\n", "1   Male     Yes          1  ...            1.0         Rural            N\n", "2   Male     Yes          0  ...            1.0         Urban            Y\n", "3   Male     Yes          0  ...            1.0         Urban            Y\n", "4   Male      No          0  ...            1.0         Urban            Y\n", "\n", "[5 rows x 12 columns]\n"]}], "source": ["#Handle Missing Values\n", "for col in df.columns:\n", "    if df[col].dtype == 'object':\n", "        df[col] = df[col].fillna(df[col].mode()[0])\n", "    else:\n", "        df[col] = df[col].fillna(df[col].mean())\n", "        \n", "print(df.head())"]}, {"cell_type": "code", "execution_count": null, "id": "283abbeb", "metadata": {}, "outputs": [], "source": ["# Encode Categorical Variables\n", "le = LabelEncoder()\n", "for col in df.columns:\n", "    if df[col].dtype == 'object':\n", "        df[col] = le.fit_transform(df[col])\n", "        \n", "print(df.head())\n", "\n", "# Split the dataset into features and target variable\n", "X = df.drop('Loan_Status', axis=1)\n", "y = df['Loan_Status']"]}, {"cell_type": "code", "execution_count": 12, "id": "fac85e6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Gender Married Dependents  ... Credit_History Property_Area  Loan_Status\n", "0   Male      No          0  ...            1.0         Urban            1\n", "1   Male     Yes          1  ...            1.0         Rural            0\n", "2   Male     Yes          0  ...            1.0         Urban            1\n", "3   Male     Yes          0  ...            1.0         Urban            1\n", "4   Male      No          0  ...            1.0         Urban            1\n", "\n", "[5 rows x 12 columns]\n"]}], "source": ["label_encoder_target = LabelEncoder()\n", "df['Loan_Status'] = label_encoder_target.fit_transform(df['Loan_Status'])\n", "\n", "with open(\"label_encoder_target.pkl\", \"wb\") as f:\n", "    pickle.dump(label_encoder_target, f)\n", "print(df.head())"]}, {"cell_type": "code", "execution_count": 14, "id": "4d1102d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Gender_Male  Married_Yes  ...  Property_Area_Semiurban  Property_Area_Urban\n", "0          1.0          0.0  ...                      0.0                  1.0\n", "1          1.0          1.0  ...                      0.0                  0.0\n", "2          1.0          1.0  ...                      0.0                  1.0\n", "3          1.0          1.0  ...                      0.0                  1.0\n", "4          1.0          0.0  ...                      0.0                  1.0\n", "\n", "[5 rows x 9 columns]\n"]}], "source": ["# One-Hot Encode Categorical Features\n", "categorical_cols = ['Gender', 'Married', 'Dependents', 'Education', 'Self_Employed', 'Property_Area']\n", "\n", "ohe = OneHotEncoder(sparse_output=False, drop=\"first\")\n", "encoded = ohe.fit_transform(df[categorical_cols])\n", "encoded_df = pd.DataFrame(encoded, columns=ohe.get_feature_names_out(categorical_cols))\n", "print(encoded_df.head())"]}, {"cell_type": "code", "execution_count": 15, "id": "45ecb8cd", "metadata": {}, "outputs": [], "source": ["# Save OneHotEncoder\n", "with open(\"onehot_encoder.pkl\", \"wb\") as f:\n", "    pickle.dump(ohe, f)"]}, {"cell_type": "code", "execution_count": 16, "id": "48c05538", "metadata": {}, "outputs": [], "source": ["# Combine with original dataframe\n", "df = pd.concat([df.drop(categorical_cols, axis=1), encoded_df], axis=1)"]}, {"cell_type": "code", "execution_count": 18, "id": "b0a335f6", "metadata": {}, "outputs": [], "source": ["X = df.drop('Loan_Status', axis=1)\n", "y = df['Loan_Status']\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n"]}, {"cell_type": "code", "execution_count": 19, "id": "8abf767f", "metadata": {}, "outputs": [], "source": ["#scale the features\n", "scaler = StandardScaler()\n", "X_train = scaler.fit_transform(X_train)\n", "X_test = scaler.transform(X_test)"]}, {"cell_type": "code", "execution_count": 20, "id": "c3bd1d85", "metadata": {}, "outputs": [], "source": ["# Save Scaler\n", "with open(\"scaler.pkl\", \"wb\") as f:\n", "    pickle.dump(scaler, f)"]}, {"cell_type": "code", "execution_count": 22, "id": "aa386158", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 0.08391466  0.18066742  1.349992   ... -0.39751587  1.25326798\n", "  -0.69203733]\n", " [-0.4293379   0.38543027 -0.20474803 ... -0.39751587  1.25326798\n", "  -0.69203733]\n", " [ 0.12609518  0.00583145  0.71185244 ... -0.39751587  1.25326798\n", "  -0.69203733]\n", " ...\n", " [-0.38226444 -0.52812706 -1.37660433 ... -0.39751587 -0.79791395\n", "   1.44500876]\n", " [ 0.76673291 -0.52812706 -0.02592784 ... -0.39751587  1.25326798\n", "  -0.69203733]\n", " [ 1.37463856 -0.52812706 -0.02592784 ... -0.39751587 -0.79791395\n", "   1.44500876]]\n", "[[ 0.60408483 -0.52812706  0.92069811 ... -0.39751587  1.25326798\n", "  -0.69203733]\n", " [-0.20122565 -0.52812706 -0.21635057 ... -0.39751587  1.25326798\n", "  -0.69203733]\n", " [-0.2253529  -0.07229346  0.10852048 ... -0.39751587 -0.79791395\n", "  -0.69203733]\n", " ...\n", " [-0.44216077  0.05245437 -0.29756833 ... -0.39751587  1.25326798\n", "  -0.69203733]\n", " [-0.50661261  0.05150931 -0.46000386 ... -0.39751587 -0.79791395\n", "   1.44500876]\n", " [ 9.75793245 -0.52812706  3.96056296 ... -0.39751587 -0.79791395\n", "   1.44500876]]\n", "83     0\n", "90     1\n", "227    1\n", "482    1\n", "464    0\n", "      ..\n", "71     1\n", "106    1\n", "270    1\n", "435    1\n", "102    1\n", "Name: Loan_Status, Length: 491, dtype: int64\n", "350    1\n", "377    1\n", "163    1\n", "609    1\n", "132    1\n", "      ..\n", "231    1\n", "312    1\n", "248    1\n", "11     1\n", "333    1\n", "Name: Loan_Status, Length: 123, dtype: int64\n"]}], "source": ["print(X_train)\n", "print(X_test)\n", "print(y_train)\n", "print(y_test)\n"]}, {"cell_type": "code", "execution_count": null, "id": "6de5e138", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "172c2c12", "metadata": {}, "source": ["ANN_IMPLEMENTATION"]}, {"cell_type": "code", "execution_count": null, "id": "91d876d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From c:\\Users\\<USER>\\anaconda3\\envs\\ann_env\\Lib\\site-packages\\keras\\src\\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.\n", "\n"]}], "source": ["import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras import layers\n", "import pickle\n", "import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 26, "id": "22f81634", "metadata": {}, "outputs": [], "source": ["\n", "# Use already available scaled data and targets from previous cells\n", "\n", "input_shape = X_train.shape[1]"]}, {"cell_type": "code", "execution_count": 29, "id": "9475f76a", "metadata": {}, "outputs": [], "source": ["#Build ANN Model\n", "model = keras.Sequential([\n", "    layers.Dense(64, activation='relu', input_shape=[input_shape]),\n", "    layers.Den<PERSON>(32, activation='relu'),\n", "    layers.Dense(1, activation='sigmoid')\n", "])\n", "\n", "model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy', tf.keras.metrics.AUC(name=\"auc\")])"]}, {"cell_type": "code", "execution_count": 33, "id": "494bf428", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/50\n", "16/16 [==============================] - 0s 18ms/step - loss: 0.4082 - accuracy: 0.8248 - auc: 0.8535 - val_loss: 0.5198 - val_accuracy: 0.7724 - val_auc: 0.7513\n", "Epoch 2/50\n", "16/16 [==============================] - 0s 11ms/step - loss: 0.4037 - accuracy: 0.8289 - auc: 0.8592 - val_loss: 0.5151 - val_accuracy: 0.7724 - val_auc: 0.7608\n", "Epoch 3/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3995 - accuracy: 0.8330 - auc: 0.8619 - val_loss: 0.5183 - val_accuracy: 0.7724 - val_auc: 0.7610\n", "Epoch 4/50\n", "16/16 [==============================] - 0s 11ms/step - loss: 0.3960 - accuracy: 0.8289 - auc: 0.8669 - val_loss: 0.5178 - val_accuracy: 0.7724 - val_auc: 0.7563\n", "Epoch 5/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3937 - accuracy: 0.8350 - auc: 0.8693 - val_loss: 0.5169 - val_accuracy: 0.7724 - val_auc: 0.7587\n", "Epoch 6/50\n", "16/16 [==============================] - 0s 11ms/step - loss: 0.3884 - accuracy: 0.8330 - auc: 0.8749 - val_loss: 0.5189 - val_accuracy: 0.7724 - val_auc: 0.7558\n", "Epoch 7/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3851 - accuracy: 0.8350 - auc: 0.8776 - val_loss: 0.5199 - val_accuracy: 0.7724 - val_auc: 0.7552\n", "Epoch 8/50\n", "16/16 [==============================] - 0s 11ms/step - loss: 0.3808 - accuracy: 0.8289 - auc: 0.8832 - val_loss: 0.5235 - val_accuracy: 0.7805 - val_auc: 0.7520\n", "Epoch 9/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3782 - accuracy: 0.8371 - auc: 0.8838 - val_loss: 0.5236 - val_accuracy: 0.7724 - val_auc: 0.7488\n", "Epoch 10/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3739 - accuracy: 0.8391 - auc: 0.8873 - val_loss: 0.5271 - val_accuracy: 0.7724 - val_auc: 0.7484\n", "Epoch 11/50\n", "16/16 [==============================] - 0s 14ms/step - loss: 0.3707 - accuracy: 0.8432 - auc: 0.8925 - val_loss: 0.5249 - val_accuracy: 0.7805 - val_auc: 0.7423\n", "Epoch 12/50\n", "16/16 [==============================] - 0s 13ms/step - loss: 0.3679 - accuracy: 0.8452 - auc: 0.8927 - val_loss: 0.5293 - val_accuracy: 0.7805 - val_auc: 0.7449\n", "Epoch 13/50\n", "16/16 [==============================] - 0s 11ms/step - loss: 0.3622 - accuracy: 0.8452 - auc: 0.8999 - val_loss: 0.5254 - val_accuracy: 0.7805 - val_auc: 0.7400\n", "Epoch 14/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3590 - accuracy: 0.8452 - auc: 0.9040 - val_loss: 0.5310 - val_accuracy: 0.7805 - val_auc: 0.7403\n", "Epoch 15/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3556 - accuracy: 0.8432 - auc: 0.9077 - val_loss: 0.5300 - val_accuracy: 0.7805 - val_auc: 0.7371\n", "Epoch 16/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.3516 - accuracy: 0.8534 - auc: 0.9083 - val_loss: 0.5360 - val_accuracy: 0.7724 - val_auc: 0.7353\n", "Epoch 17/50\n", "16/16 [==============================] - 0s 9ms/step - loss: 0.3476 - accuracy: 0.8574 - auc: 0.9105 - val_loss: 0.5370 - val_accuracy: 0.7805 - val_auc: 0.7374\n", "Epoch 18/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3465 - accuracy: 0.8452 - auc: 0.9136 - val_loss: 0.5347 - val_accuracy: 0.7724 - val_auc: 0.7414\n", "Epoch 19/50\n", "16/16 [==============================] - 0s 9ms/step - loss: 0.3415 - accuracy: 0.8574 - auc: 0.9145 - val_loss: 0.5378 - val_accuracy: 0.7724 - val_auc: 0.7390\n", "Epoch 20/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3374 - accuracy: 0.8595 - auc: 0.9178 - val_loss: 0.5390 - val_accuracy: 0.7724 - val_auc: 0.7365\n", "Epoch 21/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.3341 - accuracy: 0.8595 - auc: 0.9215 - val_loss: 0.5399 - val_accuracy: 0.7805 - val_auc: 0.7365\n", "Epoch 22/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3317 - accuracy: 0.8574 - auc: 0.9221 - val_loss: 0.5420 - val_accuracy: 0.7724 - val_auc: 0.7422\n", "Epoch 23/50\n", "16/16 [==============================] - 0s 9ms/step - loss: 0.3266 - accuracy: 0.8656 - auc: 0.9256 - val_loss: 0.5448 - val_accuracy: 0.7805 - val_auc: 0.7388\n", "Epoch 24/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3220 - accuracy: 0.8656 - auc: 0.9284 - val_loss: 0.5444 - val_accuracy: 0.7561 - val_auc: 0.7387\n", "Epoch 25/50\n", "16/16 [==============================] - 0s 9ms/step - loss: 0.3180 - accuracy: 0.8656 - auc: 0.9297 - val_loss: 0.5505 - val_accuracy: 0.7642 - val_auc: 0.7352\n", "Epoch 26/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3155 - accuracy: 0.8737 - auc: 0.9304 - val_loss: 0.5555 - val_accuracy: 0.7642 - val_auc: 0.7299\n", "Epoch 27/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.3123 - accuracy: 0.8717 - auc: 0.9322 - val_loss: 0.5502 - val_accuracy: 0.7724 - val_auc: 0.7406\n", "Epoch 28/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3089 - accuracy: 0.8778 - auc: 0.9344 - val_loss: 0.5553 - val_accuracy: 0.7642 - val_auc: 0.7369\n", "Epoch 29/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3051 - accuracy: 0.8758 - auc: 0.9358 - val_loss: 0.5628 - val_accuracy: 0.7561 - val_auc: 0.7337\n", "Epoch 30/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.3019 - accuracy: 0.8758 - auc: 0.9365 - val_loss: 0.5671 - val_accuracy: 0.7642 - val_auc: 0.7331\n", "Epoch 31/50\n", "16/16 [==============================] - 0s 11ms/step - loss: 0.2986 - accuracy: 0.8758 - auc: 0.9376 - val_loss: 0.5627 - val_accuracy: 0.7724 - val_auc: 0.7308\n", "Epoch 32/50\n", "16/16 [==============================] - 0s 10ms/step - loss: 0.2944 - accuracy: 0.8778 - auc: 0.9392 - val_loss: 0.5685 - val_accuracy: 0.7724 - val_auc: 0.7349\n", "Epoch 33/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.2910 - accuracy: 0.8778 - auc: 0.9418 - val_loss: 0.5748 - val_accuracy: 0.7724 - val_auc: 0.7339\n", "Epoch 34/50\n", "16/16 [==============================] - 0s 11ms/step - loss: 0.2897 - accuracy: 0.8778 - auc: 0.9433 - val_loss: 0.5781 - val_accuracy: 0.7561 - val_auc: 0.7302\n", "Epoch 35/50\n", "16/16 [==============================] - 0s 11ms/step - loss: 0.2844 - accuracy: 0.8798 - auc: 0.9458 - val_loss: 0.5822 - val_accuracy: 0.7561 - val_auc: 0.7321\n", "Epoch 36/50\n", "16/16 [==============================] - 0s 13ms/step - loss: 0.2822 - accuracy: 0.8839 - auc: 0.9442 - val_loss: 0.5899 - val_accuracy: 0.7642 - val_auc: 0.7249\n", "Epoch 37/50\n", "16/16 [==============================] - 0s 11ms/step - loss: 0.2789 - accuracy: 0.8819 - auc: 0.9458 - val_loss: 0.5914 - val_accuracy: 0.7398 - val_auc: 0.7272\n", "Epoch 38/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.2761 - accuracy: 0.8839 - auc: 0.9485 - val_loss: 0.5870 - val_accuracy: 0.7642 - val_auc: 0.7266\n", "Epoch 39/50\n", "16/16 [==============================] - 0s 19ms/step - loss: 0.2727 - accuracy: 0.8921 - auc: 0.9501 - val_loss: 0.5974 - val_accuracy: 0.7480 - val_auc: 0.7314\n", "Epoch 40/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.2722 - accuracy: 0.8921 - auc: 0.9508 - val_loss: 0.5969 - val_accuracy: 0.7398 - val_auc: 0.7289\n", "Epoch 41/50\n", "16/16 [==============================] - 0s 13ms/step - loss: 0.2658 - accuracy: 0.8921 - auc: 0.9525 - val_loss: 0.6046 - val_accuracy: 0.7398 - val_auc: 0.7253\n", "Epoch 42/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.2627 - accuracy: 0.8941 - auc: 0.9544 - val_loss: 0.6089 - val_accuracy: 0.7480 - val_auc: 0.7249\n", "Epoch 43/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.2601 - accuracy: 0.8961 - auc: 0.9530 - val_loss: 0.6128 - val_accuracy: 0.7398 - val_auc: 0.7262\n", "Epoch 44/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.2563 - accuracy: 0.8961 - auc: 0.9557 - val_loss: 0.6192 - val_accuracy: 0.7480 - val_auc: 0.7279\n", "Epoch 45/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.2540 - accuracy: 0.8921 - auc: 0.9572 - val_loss: 0.6226 - val_accuracy: 0.7398 - val_auc: 0.7257\n", "Epoch 46/50\n", "16/16 [==============================] - 0s 13ms/step - loss: 0.2503 - accuracy: 0.8982 - auc: 0.9590 - val_loss: 0.6282 - val_accuracy: 0.7398 - val_auc: 0.7235\n", "Epoch 47/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.2481 - accuracy: 0.9002 - auc: 0.9586 - val_loss: 0.6390 - val_accuracy: 0.7480 - val_auc: 0.7254\n", "Epoch 48/50\n", "16/16 [==============================] - 0s 13ms/step - loss: 0.2455 - accuracy: 0.9022 - auc: 0.9613 - val_loss: 0.6290 - val_accuracy: 0.7480 - val_auc: 0.7230\n", "Epoch 49/50\n", "16/16 [==============================] - 0s 12ms/step - loss: 0.2437 - accuracy: 0.9002 - auc: 0.9612 - val_loss: 0.6380 - val_accuracy: 0.7317 - val_auc: 0.7244\n", "Epoch 50/50\n", "16/16 [==============================] - 0s 13ms/step - loss: 0.2407 - accuracy: 0.9022 - auc: 0.9611 - val_loss: 0.6546 - val_accuracy: 0.7561 - val_auc: 0.7230\n"]}], "source": ["log_dir = \"logs\"\n", "tensorboard_cb = keras.callbacks.TensorBoard(log_dir=log_dir, histogram_freq=1)\n", "\n", "history = model.fit(\n", "    X_train, y_train,\n", "    validation_data=(X_test, y_test),\n", "    epochs=50,\n", "    batch_size=32,\n", "    callbacks=[tensorboard_cb],\n", "    verbose=1\n", ")"]}, {"cell_type": "code", "execution_count": 34, "id": "7ad7f46b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Model trained and saved as loan_ann_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\anaconda3\\envs\\ann_env\\Lib\\site-packages\\keras\\src\\engine\\training.py:3103: UserWarning: You are saving your model as an HDF5 file via `model.save()`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')`.\n", "  saving_api.save_model(\n"]}], "source": ["model.save(\"loan_ann_model.h5\")\n", "\n", "print(\"✅ Model trained and saved as loan_ann_model.h5\")"]}, {"cell_type": "code", "execution_count": 35, "id": "f11234ec", "metadata": {}, "outputs": [], "source": ["log_dir = \"logs\"\n", "tensorboard_cb = keras.callbacks.TensorBoard(log_dir=log_dir, histogram_freq=1)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c3d891ea", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ann_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}