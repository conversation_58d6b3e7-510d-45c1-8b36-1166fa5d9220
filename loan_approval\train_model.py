#!/usr/bin/env python3
"""
Loan Approval Prediction Model Training Script

This script trains an Artificial Neural Network (ANN) model to predict loan approval
based on applicant information and financial data.
"""

import pandas as pd
import numpy as np
import pickle
import os
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, OneHotEncoder, StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import matplotlib.pyplot as plt
import seaborn as sns

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

class LoanApprovalTrainer:
    """Class to handle loan approval model training"""
    
    def __init__(self, data_path="Lone.csv"):
        self.data_path = data_path
        self.df = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.model = None
        self.ohe = None
        self.scaler = None
        self.label_encoder_target = None
        
    def load_data(self):
        """Load and display basic information about the dataset"""
        print("📊 Loading dataset...")
        self.df = pd.read_csv(self.data_path)
        print(f"Dataset shape: {self.df.shape}")
        print(f"Columns: {list(self.df.columns)}")
        print("\nFirst 5 rows:")
        print(self.df.head())
        print("\nDataset info:")
        print(self.df.info())
        print("\nMissing values:")
        print(self.df.isnull().sum())
        
    def preprocess_data(self):
        """Preprocess the data: handle missing values, encode categorical variables"""
        print("\n🔧 Preprocessing data...")
        
        # Drop irrelevant columns
        if 'Loan_ID' in self.df.columns:
            self.df = self.df.drop('Loan_ID', axis=1)
            print("Dropped Loan_ID column")
        
        # Handle missing values
        print("Handling missing values...")
        for col in self.df.columns:
            if self.df[col].dtype == 'object':
                # Fill categorical columns with mode
                mode_value = self.df[col].mode()
                if len(mode_value) > 0:
                    self.df[col] = self.df[col].fillna(mode_value[0])
                    print(f"Filled {col} missing values with mode: {mode_value[0]}")
            else:
                # Fill numerical columns with mean
                mean_value = self.df[col].mean()
                self.df[col] = self.df[col].fillna(mean_value)
                print(f"Filled {col} missing values with mean: {mean_value:.2f}")
        
        # Encode target variable
        print("Encoding target variable...")
        self.label_encoder_target = LabelEncoder()
        self.df['Loan_Status'] = self.label_encoder_target.fit_transform(self.df['Loan_Status'])
        print(f"Target classes: {self.label_encoder_target.classes_}")
        
        # One-Hot Encode categorical features
        print("One-hot encoding categorical features...")
        categorical_cols = ['Gender', 'Married', 'Dependents', 'Education', 'Self_Employed', 'Property_Area']
        
        self.ohe = OneHotEncoder(sparse_output=False, drop="first")
        encoded = self.ohe.fit_transform(self.df[categorical_cols])
        encoded_df = pd.DataFrame(encoded, columns=self.ohe.get_feature_names_out(categorical_cols))
        
        # Combine with original dataframe
        self.df = pd.concat([self.df.drop(categorical_cols, axis=1), encoded_df], axis=1)
        
        print(f"Final dataset shape after preprocessing: {self.df.shape}")
        print(f"Features: {list(self.df.columns[:-1])}")
        
    def split_data(self, test_size=0.2, random_state=42):
        """Split data into training and testing sets"""
        print(f"\n📊 Splitting data (test_size={test_size})...")
        
        X = self.df.drop('Loan_Status', axis=1)
        y = self.df['Loan_Status']
        
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
        
        print(f"Training set shape: {self.X_train.shape}")
        print(f"Test set shape: {self.X_test.shape}")
        print(f"Training target distribution: {np.bincount(self.y_train)}")
        print(f"Test target distribution: {np.bincount(self.y_test)}")
        
    def scale_features(self):
        """Scale features using StandardScaler"""
        print("\n⚖️ Scaling features...")
        
        self.scaler = StandardScaler()
        self.X_train = self.scaler.fit_transform(self.X_train)
        self.X_test = self.scaler.transform(self.X_test)
        
        print("Features scaled successfully")
        
    def build_model(self):
        """Build the ANN model"""
        print("\n🏗️ Building ANN model...")
        
        input_shape = self.X_train.shape[1]
        
        self.model = keras.Sequential([
            layers.Dense(64, activation='relu', input_shape=[input_shape], name='hidden_layer_1'),
            layers.Dropout(0.3, name='dropout_1'),
            layers.Dense(32, activation='relu', name='hidden_layer_2'),
            layers.Dropout(0.2, name='dropout_2'),
            layers.Dense(16, activation='relu', name='hidden_layer_3'),
            layers.Dense(1, activation='sigmoid', name='output_layer')
        ])
        
        self.model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy', tf.keras.metrics.AUC(name="auc")]
        )
        
        print("Model architecture:")
        self.model.summary()
        
    def train_model(self, epochs=100, batch_size=32, validation_split=0.2):
        """Train the model"""
        print(f"\n🚀 Training model for {epochs} epochs...")
        
        # Callbacks
        early_stopping = keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=15,
            restore_best_weights=True
        )
        
        reduce_lr = keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.2,
            patience=10,
            min_lr=0.0001
        )
        
        # Create logs directory
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        tensorboard_cb = keras.callbacks.TensorBoard(log_dir=log_dir, histogram_freq=1)
        
        # Train the model
        history = self.model.fit(
            self.X_train, self.y_train,
            validation_split=validation_split,
            epochs=epochs,
            batch_size=batch_size,
            callbacks=[early_stopping, reduce_lr, tensorboard_cb],
            verbose=1
        )
        
        return history
        
    def evaluate_model(self):
        """Evaluate the model on test data"""
        print("\n📈 Evaluating model...")
        
        # Predictions
        y_pred_proba = self.model.predict(self.X_test, verbose=0)
        y_pred = (y_pred_proba > 0.5).astype(int).flatten()
        
        # Metrics
        accuracy = accuracy_score(self.y_test, y_pred)
        print(f"Test Accuracy: {accuracy:.4f}")
        
        print("\nClassification Report:")
        print(classification_report(self.y_test, y_pred, 
                                  target_names=self.label_encoder_target.classes_))
        
        print("\nConfusion Matrix:")
        cm = confusion_matrix(self.y_test, y_pred)
        print(cm)
        
        return accuracy, y_pred, y_pred_proba
        
    def save_model_and_encoders(self):
        """Save the trained model and preprocessing objects"""
        print("\n💾 Saving model and encoders...")
        
        # Save model
        self.model.save("loan_ann_model.h5")
        print("✅ Model saved as loan_ann_model.h5")
        
        # Save encoders and scaler
        with open("onehot_encoder.pkl", "wb") as f:
            pickle.dump(self.ohe, f)
        print("✅ OneHotEncoder saved as onehot_encoder.pkl")
        
        with open("scaler.pkl", "wb") as f:
            pickle.dump(self.scaler, f)
        print("✅ StandardScaler saved as scaler.pkl")
        
        with open("label_encoder_target.pkl", "wb") as f:
            pickle.dump(self.label_encoder_target, f)
        print("✅ LabelEncoder saved as label_encoder_target.pkl")
        
    def plot_training_history(self, history):
        """Plot training history"""
        print("\n📊 Plotting training history...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Accuracy
        axes[0, 0].plot(history.history['accuracy'], label='Training Accuracy')
        axes[0, 0].plot(history.history['val_accuracy'], label='Validation Accuracy')
        axes[0, 0].set_title('Model Accuracy')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].legend()
        
        # Loss
        axes[0, 1].plot(history.history['loss'], label='Training Loss')
        axes[0, 1].plot(history.history['val_loss'], label='Validation Loss')
        axes[0, 1].set_title('Model Loss')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        
        # AUC
        axes[1, 0].plot(history.history['auc'], label='Training AUC')
        axes[1, 0].plot(history.history['val_auc'], label='Validation AUC')
        axes[1, 0].set_title('Model AUC')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('AUC')
        axes[1, 0].legend()
        
        # Feature importance (placeholder)
        axes[1, 1].text(0.5, 0.5, 'Feature importance\nwould require additional\nanalysis', 
                       ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].set_title('Feature Importance')
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("✅ Training history plot saved as training_history.png")

def main():
    """Main training function"""
    print("🏦 Loan Approval Prediction Model Training")
    print("=" * 50)
    
    # Initialize trainer
    trainer = LoanApprovalTrainer()
    
    # Load and preprocess data
    trainer.load_data()
    trainer.preprocess_data()
    trainer.split_data()
    trainer.scale_features()
    
    # Build and train model
    trainer.build_model()
    history = trainer.train_model(epochs=100, batch_size=32)
    
    # Evaluate model
    accuracy, y_pred, y_pred_proba = trainer.evaluate_model()
    
    # Save everything
    trainer.save_model_and_encoders()
    
    # Plot results
    trainer.plot_training_history(history)
    
    print(f"\n🎉 Training completed! Final test accuracy: {accuracy:.4f}")
    print("All files saved successfully. You can now run the Streamlit app.")

if __name__ == "__main__":
    main()
