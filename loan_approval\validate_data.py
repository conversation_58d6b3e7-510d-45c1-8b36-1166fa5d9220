#!/usr/bin/env python3
"""
Data Validation Script for Loan Approval Prediction

This script validates the data preprocessing pipeline and ensures all components
are working correctly together.
"""

import pandas as pd
import numpy as np
import pickle
import tensorflow as tf
import os

def load_preprocessing_objects():
    """Load all preprocessing objects"""
    try:
        with open("onehot_encoder.pkl", "rb") as f:
            ohe = pickle.load(f)
        
        with open("scaler.pkl", "rb") as f:
            scaler = pickle.load(f)
        
        with open("label_encoder_target.pkl", "rb") as f:
            label_encoder_target = pickle.load(f)
        
        model = tf.keras.models.load_model("loan_ann_model.h5")
        
        return ohe, scaler, label_encoder_target, model
    except Exception as e:
        print(f"Error loading preprocessing objects: {e}")
        return None, None, None, None

def validate_sample_prediction():
    """Test the complete pipeline with a sample prediction"""
    print("🧪 Testing sample prediction...")
    
    # Load preprocessing objects
    ohe, scaler, label_encoder_target, model = load_preprocessing_objects()
    
    if any(obj is None for obj in [ohe, scaler, label_encoder_target, model]):
        print("❌ Failed to load preprocessing objects")
        return False
    
    # Sample data that matches the expected format
    sample_data = {
        "Gender": ["Male"],
        "Married": ["Yes"],
        "Dependents": ["1"],
        "Education": ["Graduate"],
        "Self_Employed": ["No"],
        "ApplicantIncome": [5000],
        "CoapplicantIncome": [2000],
        "LoanAmount": [150],
        "Loan_Amount_Term": [360],
        "Credit_History": [1.0],
        "Property_Area": ["Urban"]
    }
    
    try:
        # Convert to DataFrame
        df_new = pd.DataFrame(sample_data)
        print(f"✅ Sample data created: {df_new.shape}")
        
        # One-hot encode categorical features
        categorical_cols = ['Gender', 'Married', 'Dependents', 'Education', 'Self_Employed', 'Property_Area']
        encoded = ohe.transform(df_new[categorical_cols])
        encoded_df = pd.DataFrame(encoded, columns=ohe.get_feature_names_out(categorical_cols))
        print(f"✅ One-hot encoding successful: {encoded_df.shape}")
        
        # Combine features
        df_processed = pd.concat([df_new.drop(categorical_cols, axis=1).reset_index(drop=True),
                                 encoded_df.reset_index(drop=True)], axis=1)
        print(f"✅ Feature combination successful: {df_processed.shape}")
        
        # Scale features
        X_scaled = scaler.transform(df_processed)
        print(f"✅ Feature scaling successful: {X_scaled.shape}")
        
        # Make prediction
        probability = model.predict(X_scaled, verbose=0)[0][0]
        prediction = "Approved" if probability >= 0.5 else "Rejected"
        
        print(f"✅ Prediction successful: {prediction} (probability: {probability:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in prediction pipeline: {e}")
        return False

def validate_data_consistency():
    """Validate data consistency and preprocessing objects"""
    print("\n🔍 Validating data consistency...")
    
    # Load original data
    try:
        df = pd.read_csv("Lone.csv")
        print(f"✅ Original data loaded: {df.shape}")
        print(f"Columns: {list(df.columns)}")
    except Exception as e:
        print(f"❌ Error loading original data: {e}")
        return False
    
    # Check for missing values
    missing_values = df.isnull().sum()
    print(f"Missing values per column:")
    for col, missing in missing_values.items():
        if missing > 0:
            print(f"  {col}: {missing}")
    
    # Check categorical values
    categorical_cols = ['Gender', 'Married', 'Dependents', 'Education', 'Self_Employed', 'Property_Area']
    print(f"\nCategorical column unique values:")
    for col in categorical_cols:
        if col in df.columns:
            unique_vals = df[col].unique()
            print(f"  {col}: {unique_vals}")
    
    # Check target variable
    if 'Loan_Status' in df.columns:
        target_counts = df['Loan_Status'].value_counts()
        print(f"\nTarget variable distribution:")
        print(target_counts)
    
    return True

def validate_model_architecture():
    """Validate model architecture and compatibility"""
    print("\n🏗️ Validating model architecture...")
    
    try:
        model = tf.keras.models.load_model("loan_ann_model.h5")
        print("✅ Model loaded successfully")
        
        print(f"Model input shape: {model.input_shape}")
        print(f"Model output shape: {model.output_shape}")
        
        # Print model summary
        print("\nModel architecture:")
        model.summary()
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

def validate_encoders():
    """Validate encoder objects"""
    print("\n🔧 Validating encoders...")
    
    try:
        # Load encoders
        with open("onehot_encoder.pkl", "rb") as f:
            ohe = pickle.load(f)
        
        with open("scaler.pkl", "rb") as f:
            scaler = pickle.load(f)
        
        with open("label_encoder_target.pkl", "rb") as f:
            label_encoder_target = pickle.load(f)
        
        print("✅ All encoders loaded successfully")
        
        # Check OneHotEncoder
        print(f"OneHotEncoder feature names: {ohe.get_feature_names_out()}")
        print(f"OneHotEncoder categories: {len(ohe.categories_)} categories")
        
        # Check StandardScaler
        print(f"StandardScaler features: {len(scaler.feature_names_in_)} features")
        print(f"StandardScaler mean shape: {scaler.mean_.shape}")
        print(f"StandardScaler scale shape: {scaler.scale_.shape}")
        
        # Check LabelEncoder
        print(f"LabelEncoder classes: {label_encoder_target.classes_}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error validating encoders: {e}")
        return False

def main():
    """Main validation function"""
    print("🔍 Loan Approval Model Validation")
    print("=" * 50)
    
    # Change to the correct directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Run all validations
    validations = [
        ("Data Consistency", validate_data_consistency),
        ("Model Architecture", validate_model_architecture),
        ("Encoders", validate_encoders),
        ("Sample Prediction", validate_sample_prediction)
    ]
    
    results = {}
    for name, validation_func in validations:
        print(f"\n{'='*20} {name} {'='*20}")
        results[name] = validation_func()
    
    # Summary
    print(f"\n{'='*20} VALIDATION SUMMARY {'='*20}")
    all_passed = True
    for name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All validations passed! The system is ready to use.")
    else:
        print("\n⚠️ Some validations failed. Please check the errors above.")
        print("You may need to retrain the model using train_model.py")
    
    return all_passed

if __name__ == "__main__":
    main()
