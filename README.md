# 🏦 Loan Approval Prediction System

A machine learning-powered web application that predicts loan approval probability using an Artificial Neural Network (ANN). The system analyzes applicant information and financial data to provide instant loan approval predictions.

## 🌟 Features

- **Interactive Web Interface**: User-friendly Streamlit application
- **Real-time Predictions**: Instant loan approval probability calculation
- **Comprehensive Data Processing**: Handles missing values and categorical encoding
- **Model Validation**: Built-in testing and validation suite
- **Professional Architecture**: Clean, modular, and maintainable code

## 📊 Model Performance

- **Algorithm**: Artificial Neural Network (ANN)
- **Architecture**: 3 hidden layers with dropout regularization
- **Input Features**: 11 applicant and financial features
- **Output**: Binary classification (Approved/Rejected) with probability score

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Installation

1. **Clone or download the project**
   ```bash
   cd loan_approval
   ```

2. **Install dependencies**
   ```bash
   pip install -r ../requirements.txt
   ```

3. **Validate the system** (optional but recommended)
   ```bash
   python validate_data.py
   ```

4. **Run the web application**
   ```bash
   streamlit run app.py
   ```

5. **Open your browser** and navigate to `http://localhost:8501`

## 📁 Project Structure

```
loan_approval/
├── app.py                    # Main Streamlit web application
├── train_model.py           # Model training script
├── validate_data.py         # Data validation and testing
├── test_app.py             # Comprehensive test suite
├── Lone.csv                # Training dataset
├── loan_ann_model.h5       # Trained neural network model
├── onehot_encoder.pkl      # Categorical feature encoder
├── scaler.pkl              # Feature scaler
├── label_encoder_target.pkl # Target variable encoder
├── loan_ann.ipynb          # Original training notebook
├── loan_predict.ipynb      # Original prediction notebook
└── logs/                   # Training logs and TensorBoard data
```

## 🔧 Usage

### Web Application

1. **Launch the app**: `streamlit run app.py`
2. **Fill in applicant details**:
   - Personal information (Gender, Marital Status, Dependents, Education, Employment)
   - Financial information (Income, Loan Amount, Credit History, Property Area)
3. **Click "Predict Loan Approval"**
4. **View results**: Probability score and approval decision

### Model Training

To retrain the model with new data:

```bash
python train_model.py
```

This will:
- Load and preprocess the data
- Train a new neural network model
- Save all preprocessing objects
- Generate training visualizations

### Testing and Validation

Run the comprehensive test suite:

```bash
python test_app.py
```

Validate data consistency and model compatibility:

```bash
python validate_data.py
```

## 📋 Input Features

| Feature | Type | Description | Values |
|---------|------|-------------|---------|
| Gender | Categorical | Applicant's gender | Male, Female |
| Married | Categorical | Marital status | Yes, No |
| Dependents | Categorical | Number of dependents | 0, 1, 2, 3+ |
| Education | Categorical | Education level | Graduate, Not Graduate |
| Self_Employed | Categorical | Employment type | Yes, No |
| ApplicantIncome | Numerical | Primary applicant's income | > 0 |
| CoapplicantIncome | Numerical | Co-applicant's income | ≥ 0 |
| LoanAmount | Numerical | Requested loan amount | > 0 |
| Loan_Amount_Term | Categorical | Loan term in days | 120, 180, 240, 300, 360, 480 |
| Credit_History | Categorical | Credit history status | 1.0 (Good), 0.0 (Poor) |
| Property_Area | Categorical | Property location | Urban, Semiurban, Rural |

## 🧠 Model Architecture

```
Input Layer (11 features)
    ↓
Dense Layer (64 neurons, ReLU)
    ↓
Dropout (30%)
    ↓
Dense Layer (32 neurons, ReLU)
    ↓
Dropout (20%)
    ↓
Dense Layer (16 neurons, ReLU)
    ↓
Output Layer (1 neuron, Sigmoid)
```

**Training Configuration:**
- Optimizer: Adam
- Loss Function: Binary Crossentropy
- Metrics: Accuracy, AUC
- Early Stopping: Patience of 15 epochs
- Learning Rate Reduction: Factor of 0.2

## 🔍 Data Processing Pipeline

1. **Data Loading**: Load CSV dataset
2. **Missing Value Handling**: 
   - Categorical: Fill with mode
   - Numerical: Fill with mean
3. **Feature Encoding**:
   - One-hot encoding for categorical features
   - Label encoding for target variable
4. **Feature Scaling**: StandardScaler normalization
5. **Train/Test Split**: 80/20 split with stratification

## 🧪 Testing

The project includes comprehensive testing:

- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end pipeline testing
- **Data Validation**: Input/output validation
- **Model Testing**: Prediction accuracy and consistency

## 📈 Performance Monitoring

- **TensorBoard Integration**: Training visualization
- **Model Metrics**: Accuracy, AUC, Loss tracking
- **Validation Curves**: Overfitting detection
- **Feature Importance**: Model interpretability

## 🛠️ Troubleshooting

### Common Issues

1. **Missing Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Model Loading Errors**
   ```bash
   python validate_data.py
   ```

3. **Data Processing Issues**
   ```bash
   python train_model.py
   ```

4. **Port Already in Use**
   ```bash
   streamlit run app.py --server.port 8502
   ```

### Error Messages

- **"Error loading model or encoders"**: Run `validate_data.py` to check file integrity
- **"Validation errors"**: Check input values are within expected ranges
- **"Feature mismatch"**: Retrain model if data structure changed

## 🔄 Model Updates

To update the model with new data:

1. Replace `Lone.csv` with new dataset
2. Run `python train_model.py`
3. Validate with `python validate_data.py`
4. Test with `python test_app.py`

## 📊 Dataset Information

- **Source**: Loan approval dataset
- **Size**: 614 records
- **Features**: 11 input features + 1 target
- **Target Distribution**: Balanced classification problem
- **Missing Values**: Handled automatically

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `python test_app.py`
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙋‍♂️ Support

For questions or issues:
1. Check the troubleshooting section
2. Run the validation script
3. Review the test results
4. Open an issue with detailed error information

---

**Built with ❤️ using Python, TensorFlow, and Streamlit**
