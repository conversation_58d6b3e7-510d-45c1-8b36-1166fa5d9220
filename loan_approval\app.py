# app.py

import streamlit as st
import pandas as pd
import numpy as np
import pickle
import tensorflow as tf

# -------------------------------
# 1. Load Saved Encoders & Model
# -------------------------------
with open("onehot_encoder.pkl", "rb") as f:
    ohe = pickle.load(f)

with open("scaler.pkl", "rb") as f:
    scaler = pickle.load(f)

model = tf.keras.models.load_model("loan_ann_model.h5")

# -------------------------------
# 2. Streamlit UI
# -------------------------------
st.title("🏦 Loan Approval Prediction System (ANN)")
st.write("Enter applicant details to predict loan approval probability.")

gender = st.selectbox("Gender", ["Male", "Female"])
married = st.selectbox("Married", ["Yes", "No"])
dependents = st.selectbox("Dependents", ["0", "1", "2", "3+"])
education = st.selectbox("Education", ["Graduate", "Not Graduate"])
self_employed = st.selectbox("Self Employed", ["Yes", "No"])
applicant_income = st.number_input("Applicant Income", min_value=0)
coapplicant_income = st.number_input("Coapplicant Income", min_value=0)
loan_amount = st.number_input("Loan Amount", min_value=0)
loan_term = st.selectbox("Loan Amount Term (in days)", [360, 120, 180, 240, 300, 480])
credit_history = st.selectbox("Credit History", [1.0, 0.0])
property_area = st.selectbox("Property Area", ["Urban", "Semiurban", "Rural"])

if st.button("🔍 Predict Loan Approval"):
    # -------------------------------
    # 3. Convert Inputs to DataFrame
    # -------------------------------
    new_data = {
        "Gender": [gender],
        "Married": [married],
        "Dependents": [dependents],
        "Education": [education],
        "Self_Employed": [self_employed],
        "ApplicantIncome": [applicant_income],
        "CoapplicantIncome": [coapplicant_income],
        "LoanAmount": [loan_amount],
        "Loan_Amount_Term": [loan_term],
        "Credit_History": [credit_history],
        "Property_Area": [property_area]
    }

    df_new = pd.DataFrame(new_data)

    # -------------------------------
    # 4. One-Hot Encode Categorical
    # -------------------------------
    categorical_cols = ['Gender', 'Married', 'Dependents', 'Education', 'Self_Employed', 'Property_Area']
    encoded = ohe.transform(df_new[categorical_cols])
    encoded_df = pd.DataFrame(encoded, columns=ohe.get_feature_names_out(categorical_cols))

    # Drop old categorical cols & join with numeric
    df_new_processed = pd.concat([df_new.drop(categorical_cols, axis=1).reset_index(drop=True),
                                  encoded_df.reset_index(drop=True)], axis=1)

    # -------------------------------
    # 5. Scale Features
    # -------------------------------
    X_scaled = scaler.transform(df_new_processed)

    # -------------------------------
    # 6. Predict
    # -------------------------------
    probability = model.predict(X_scaled)[0][0]
    prediction = "✅ Loan Approved" if probability >= 0.5 else "❌ Loan Rejected"

    # -------------------------------
    # 7. Show Results
    # -------------------------------
    st.subheader("📊 Prediction Results")
    st.write("**Probability of Approval:**", round(probability, 3))
    st.write("**Final Decision:**", prediction)
