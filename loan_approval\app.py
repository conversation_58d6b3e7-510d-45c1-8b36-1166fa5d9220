# app.py

import streamlit as st
import pandas as pd
import numpy as np
import pickle
import tensorflow as tf
import os

# -------------------------------
# 1. Load Saved Encoders & Model
# -------------------------------
@st.cache_resource
def load_model_and_encoders():
    """Load the trained model and preprocessing objects"""
    try:
        # Get the directory where this script is located
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # Load encoders and scaler
        with open(os.path.join(script_dir, "onehot_encoder.pkl"), "rb") as f:
            ohe = pickle.load(f)

        with open(os.path.join(script_dir, "scaler.pkl"), "rb") as f:
            scaler = pickle.load(f)

        # Load model
        model = tf.keras.models.load_model(os.path.join(script_dir, "loan_ann_model.h5"))

        return model, ohe, scaler
    except Exception as e:
        st.error(f"Error loading model or encoders: {str(e)}")
        st.stop()

model, ohe, scaler = load_model_and_encoders()

# -------------------------------
# 2. Streamlit UI
# -------------------------------
st.title("🏦 Loan Approval Prediction System (ANN)")
st.write("Enter applicant details to predict loan approval probability.")

# Create two columns for better layout
col1, col2 = st.columns(2)

with col1:
    st.subheader("👤 Personal Information")
    gender = st.selectbox("Gender", ["Male", "Female"])
    married = st.selectbox("Married", ["Yes", "No"])
    dependents = st.selectbox("Dependents", ["0", "1", "2", "3+"])
    education = st.selectbox("Education", ["Graduate", "Not Graduate"])
    self_employed = st.selectbox("Self Employed", ["Yes", "No"])

with col2:
    st.subheader("💰 Financial Information")
    applicant_income = st.number_input("Applicant Income ($)", min_value=0, value=5000, step=100)
    coapplicant_income = st.number_input("Coapplicant Income ($)", min_value=0, value=0, step=100)
    loan_amount = st.number_input("Loan Amount ($)", min_value=0, value=150, step=10)
    loan_term = st.selectbox("Loan Amount Term (in days)", [360, 120, 180, 240, 300, 480])
    credit_history = st.selectbox("Credit History", [1.0, 0.0], format_func=lambda x: "Good" if x == 1.0 else "Poor")
    property_area = st.selectbox("Property Area", ["Urban", "Semiurban", "Rural"])

# Input validation
def validate_inputs():
    """Validate user inputs"""
    errors = []

    if applicant_income <= 0:
        errors.append("Applicant income must be greater than 0")

    if loan_amount <= 0:
        errors.append("Loan amount must be greater than 0")

    if loan_amount > applicant_income + coapplicant_income:
        st.warning("⚠️ Loan amount is higher than total income. This may affect approval chances.")

    return errors

# Prediction button
if st.button("🔍 Predict Loan Approval", type="primary"):
    # Validate inputs
    validation_errors = validate_inputs()

    if validation_errors:
        for error in validation_errors:
            st.error(error)
    else:
        try:
            # -------------------------------
            # 3. Convert Inputs to DataFrame
            # -------------------------------
            new_data = {
                "Gender": [gender],
                "Married": [married],
                "Dependents": [dependents],
                "Education": [education],
                "Self_Employed": [self_employed],
                "ApplicantIncome": [applicant_income],
                "CoapplicantIncome": [coapplicant_income],
                "LoanAmount": [loan_amount],
                "Loan_Amount_Term": [loan_term],
                "Credit_History": [credit_history],
                "Property_Area": [property_area]
            }

            df_new = pd.DataFrame(new_data)

            # -------------------------------
            # 4. One-Hot Encode Categorical
            # -------------------------------
            categorical_cols = ['Gender', 'Married', 'Dependents', 'Education', 'Self_Employed', 'Property_Area']
            encoded = ohe.transform(df_new[categorical_cols])
            encoded_df = pd.DataFrame(encoded, columns=ohe.get_feature_names_out(categorical_cols))

            # Drop old categorical cols & join with numeric
            df_new_processed = pd.concat([df_new.drop(categorical_cols, axis=1).reset_index(drop=True),
                                          encoded_df.reset_index(drop=True)], axis=1)

            # -------------------------------
            # 5. Scale Features
            # -------------------------------
            X_scaled = scaler.transform(df_new_processed)

            # -------------------------------
            # 6. Predict
            # -------------------------------
            with st.spinner("Making prediction..."):
                probability = model.predict(X_scaled, verbose=0)[0][0]
                prediction = "✅ Loan Approved" if probability >= 0.5 else "❌ Loan Rejected"

            # -------------------------------
            # 7. Show Results
            # -------------------------------
            st.subheader("📊 Prediction Results")

            # Create columns for results
            result_col1, result_col2 = st.columns(2)

            with result_col1:
                st.metric("Approval Probability", f"{probability:.1%}")

            with result_col2:
                st.metric("Decision", prediction.split(" ", 1)[1])

            # Add probability bar
            st.progress(probability)

            # Additional insights
            if probability >= 0.8:
                st.success("🎉 High chance of approval!")
            elif probability >= 0.6:
                st.info("👍 Good chance of approval")
            elif probability >= 0.4:
                st.warning("⚠️ Moderate chance of approval")
            else:
                st.error("📉 Low chance of approval")

        except Exception as e:
            st.error(f"An error occurred during prediction: {str(e)}")
            st.error("Please check your inputs and try again.")
