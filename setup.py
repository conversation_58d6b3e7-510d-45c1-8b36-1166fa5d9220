#!/usr/bin/env python3
"""
Setup script for Loan Approval Prediction System

This script helps set up the environment and validate the installation.
"""

import os
import sys
import subprocess
import importlib.util

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_requirements():
    """Install required packages"""
    print("\n📦 Installing requirements...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def check_required_packages():
    """Check if all required packages are installed"""
    print("\n🔍 Checking required packages...")
    
    required_packages = [
        "streamlit",
        "tensorflow",
        "pandas",
        "numpy",
        "scikit-learn",
        "matplotlib",
        "seaborn"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        spec = importlib.util.find_spec(package)
        if spec is None:
            missing_packages.append(package)
            print(f"❌ {package} not found")
        else:
            print(f"✅ {package} found")
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        return False
    
    return True

def validate_project_structure():
    """Validate project structure and required files"""
    print("\n📁 Validating project structure...")
    
    required_files = [
        "loan_approval/app.py",
        "loan_approval/train_model.py",
        "loan_approval/validate_data.py",
        "loan_approval/test_app.py",
        "loan_approval/Lone.csv",
        "loan_approval/loan_ann_model.h5",
        "loan_approval/onehot_encoder.pkl",
        "loan_approval/scaler.pkl",
        "loan_approval/label_encoder_target.pkl"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}")
    
    if missing_files:
        print(f"\n⚠️ Missing files: {', '.join(missing_files)}")
        if any("pkl" in f or "h5" in f for f in missing_files):
            print("💡 Tip: Run 'python loan_approval/train_model.py' to generate missing model files")
        return False
    
    return True

def run_validation():
    """Run system validation"""
    print("\n🧪 Running system validation...")
    
    try:
        os.chdir("loan_approval")
        result = subprocess.run([sys.executable, "validate_data.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ System validation passed")
            return True
        else:
            print("❌ System validation failed")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running validation: {e}")
        return False
    finally:
        os.chdir("..")

def run_tests():
    """Run the test suite"""
    print("\n🧪 Running tests...")
    
    try:
        os.chdir("loan_approval")
        result = subprocess.run([sys.executable, "test_app.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("❌ Some tests failed")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False
    finally:
        os.chdir("..")

def launch_app():
    """Launch the Streamlit app"""
    print("\n🚀 Launching Streamlit app...")
    print("The app will open in your default browser.")
    print("Press Ctrl+C to stop the app.")
    
    try:
        os.chdir("loan_approval")
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"])
    except KeyboardInterrupt:
        print("\n👋 App stopped by user")
    except Exception as e:
        print(f"❌ Error launching app: {e}")
    finally:
        os.chdir("..")

def main():
    """Main setup function"""
    print("🏦 Loan Approval Prediction System Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("\n💡 Try running: pip install -r requirements.txt")
        sys.exit(1)
    
    # Check packages
    if not check_required_packages():
        print("\n💡 Some packages are missing. Try reinstalling requirements.")
        sys.exit(1)
    
    # Validate project structure
    if not validate_project_structure():
        print("\n💡 Some files are missing. Check the project structure.")
        
        # Ask if user wants to retrain the model
        response = input("\nDo you want to retrain the model? (y/n): ").lower().strip()
        if response == 'y':
            print("\n🏋️ Training model...")
            try:
                os.chdir("loan_approval")
                subprocess.run([sys.executable, "train_model.py"])
                os.chdir("..")
                print("✅ Model training completed")
            except Exception as e:
                print(f"❌ Error training model: {e}")
                os.chdir("..")
                sys.exit(1)
        else:
            sys.exit(1)
    
    # Run validation
    if not run_validation():
        print("\n💡 System validation failed. Check the error messages above.")
        response = input("\nDo you want to continue anyway? (y/n): ").lower().strip()
        if response != 'y':
            sys.exit(1)
    
    # Run tests
    if not run_tests():
        print("\n💡 Some tests failed. The system might still work, but there could be issues.")
        response = input("\nDo you want to continue anyway? (y/n): ").lower().strip()
        if response != 'y':
            sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run 'streamlit run loan_approval/app.py' to start the web app")
    print("2. Open http://localhost:8501 in your browser")
    print("3. Enter loan application details and get predictions!")
    
    # Ask if user wants to launch the app now
    response = input("\nDo you want to launch the app now? (y/n): ").lower().strip()
    if response == 'y':
        launch_app()

if __name__ == "__main__":
    main()
