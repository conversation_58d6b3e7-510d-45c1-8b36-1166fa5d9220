{"cells": [{"cell_type": "code", "execution_count": 1, "id": "36a8d457", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From c:\\Users\\<USER>\\anaconda3\\envs\\ann_env\\Lib\\site-packages\\keras\\src\\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.\n", "\n"]}], "source": ["# loan_predict.py\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import pickle\n", "import tensorflow as tf"]}, {"cell_type": "code", "execution_count": 2, "id": "d7cf1b12", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From c:\\Users\\<USER>\\anaconda3\\envs\\ann_env\\Lib\\site-packages\\keras\\src\\backend.py:1398: The name tf.executing_eagerly_outside_functions is deprecated. Please use tf.compat.v1.executing_eagerly_outside_functions instead.\n", "\n"]}], "source": ["with open(\"label_encoder_target.pkl\", \"rb\") as f:\n", "    label_encoder_target = pickle.load(f)\n", "\n", "with open(\"onehot_encoder.pkl\", \"rb\") as f:\n", "    ohe = pickle.load(f)\n", "\n", "with open(\"scaler.pkl\", \"rb\") as f:\n", "    scaler = pickle.load(f)\n", "\n", "model = tf.keras.models.load_model(\"loan_ann_model.h5\")"]}, {"cell_type": "code", "execution_count": 3, "id": "ac195930", "metadata": {}, "outputs": [], "source": ["# Format should match original dataset columns\n", "new_data = {\n", "    \"Gender\": [\"Male\"],\n", "    \"Married\": [\"Yes\"],\n", "    \"Dependents\": [\"1\"],\n", "    \"Education\": [\"Graduate\"],\n", "    \"Self_Employed\": [\"No\"],\n", "    \"ApplicantIncome\": [5000],\n", "    \"CoapplicantIncome\": [2000],\n", "    \"LoanAmount\": [150],\n", "    \"Loan_Amount_Term\": [360],\n", "    \"Credit_History\": [1.0],\n", "    \"Property_Area\": [\"Urban\"]\n", "}"]}, {"cell_type": "code", "execution_count": 4, "id": "ffc57513", "metadata": {}, "outputs": [], "source": ["df_new = pd.DataFrame(new_data)"]}, {"cell_type": "code", "execution_count": 5, "id": "6f36c902", "metadata": {}, "outputs": [], "source": ["categorical_cols = ['Gender', 'Married', 'Dependents', 'Education', 'Self_Employed', 'Property_Area']\n", "encoded = ohe.transform(df_new[categorical_cols])\n", "encoded_df = pd.DataFrame(encoded, columns=ohe.get_feature_names_out(categorical_cols))"]}, {"cell_type": "code", "execution_count": 6, "id": "b6bd7959", "metadata": {}, "outputs": [], "source": ["df_new_processed = pd.concat([df_new.drop(categorical_cols, axis=1).reset_index(drop=True),\n", "                              encoded_df.reset_index(drop=True)], axis=1)"]}, {"cell_type": "code", "execution_count": 7, "id": "864b2b21", "metadata": {}, "outputs": [], "source": ["X_scaled = scaler.transform(df_new_processed)"]}, {"cell_type": "code", "execution_count": 8, "id": "ef81019f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1/1 [==============================] - 0s 235ms/step\n", "Loan Prediction: Approved\n", "Probability: 0.92\n"]}], "source": ["probability = model.predict(X_scaled)[0][0]\n", "prediction = \"Approved\" if probability >= 0.5 else \"Rejected\"\n", "\n", "print(f\"Loan Prediction: {prediction}\")\n", "print(f\"Probability: {probability:.2f}\")\n"]}, {"cell_type": "code", "execution_count": 9, "id": "29d70911", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Loan Prediction Result\n", "--------------------------\n", "Probability of Approval: 0.919\n", "Final Decision: Approved\n"]}], "source": ["print(\"📊 Loan Prediction Result\")\n", "print(\"--------------------------\")\n", "print(\"Probability of Approval:\", round(probability, 3))\n", "print(\"Final Decision:\", prediction)"]}, {"cell_type": "code", "execution_count": null, "id": "f2703827", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ann_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}